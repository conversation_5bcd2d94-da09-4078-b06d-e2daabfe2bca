tableextension 60010 "Quality Control Header MXW" extends "Quality Control Header QCM"
{
    fields
    {
        field(60000; "Expiration Date MXW"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the expiration date for the item lot.';
            
            trigger OnValidate()
            var
                WarehouseReceiptLine: Record "Warehouse Receipt Line";
                MaxwellBasicFunctions: Codeunit "Maxwell Basic Functions MXW";
                ExpirationDateUpdatedMsg: Label 'Expiration date has been updated on related warehouse receipt line(s).';
            begin
                // Update related warehouse receipt lines when expiration date is changed
                if Rec."Expiration Date MXW" <> xRec."Expiration Date MXW" then begin
                    WarehouseReceiptLine.SetRange("No.", Rec."Source Document No.");
                    WarehouseReceiptLine.SetRange("Line No.", Rec."Source Document Line No.");
                    WarehouseReceiptLine.SetRange("Quality Control Doc. No. MXW", Rec."No.");
                    
                    if WarehouseReceiptLine.FindSet() then
                        repeat
                            WarehouseReceiptLine."Expiration Date MXW" := Rec."Expiration Date MXW";
                            WarehouseReceiptLine.Modify(true);
                            
                            // Also update lot and package information
                            MaxwellBasicFunctions.UpdateLotAndPackageExpirationDate(
                                WarehouseReceiptLine."Item No.", 
                                WarehouseReceiptLine."Variant Code", 
                                WarehouseReceiptLine."Lot No. MXW", 
                                Rec."Expiration Date MXW",
                                WarehouseReceiptLine."No.",
                                WarehouseReceiptLine."Line No."
                            );
                        until WarehouseReceiptLine.Next() = 0;
                    
                    Message(ExpirationDateUpdatedMsg);
                end;
            end;
        }
    }
}
